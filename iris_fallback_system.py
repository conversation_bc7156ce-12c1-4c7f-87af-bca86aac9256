#!/usr/bin/env python3
"""
IRIS Fallback System
===================

Provides basic IRIS functionality when advanced dependencies are not available.
This ensures IRIS can still operate and provide useful responses even with minimal setup.
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class FallbackResponse:
    """Simple response structure for fallback system"""
    response: str
    confidence: float
    processing_time: float
    source: str
    metadata: Dict[str, Any]

class IRISFallbackSystem:
    """Fallback system that provides basic IRIS functionality"""
    
    def __init__(self):
        self.initialized = False
        self.knowledge_base = self._create_knowledge_base()
        
    async def initialize(self):
        """Initialize the fallback system"""
        logger.info("Initializing IRIS Fallback System...")
        self.initialized = True
        logger.info("✅ IRIS Fallback System ready")
        
    def _create_knowledge_base(self) -> Dict[str, Dict[str, str]]:
        """Create a basic knowledge base for common queries"""
        return {
            'insurance': {
                'life_insurance': "Life insurance provides financial protection for your beneficiaries when you pass away. There are two main types: term life (temporary, lower cost) and whole life (permanent, includes savings component).",
                'auto_insurance': "Auto insurance protects you financially from car accidents, theft, and damage. Most states require liability coverage, but comprehensive and collision provide additional protection.",
                'health_insurance': "Health insurance helps pay for medical expenses including doctor visits, hospital stays, and prescriptions. Plans vary in deductibles, copays, and coverage networks.",
                'homeowners_insurance': "Homeowners insurance protects your home and belongings from damage, theft, and liability claims. It typically covers the structure, personal property, and liability protection.",
                'disability_insurance': "Disability insurance replaces a portion of your income if you become unable to work due to illness or injury. It can be short-term or long-term coverage.",
                'umbrella_insurance': "Umbrella insurance provides additional liability coverage beyond your auto and homeowners policies. It protects against large claims and lawsuits."
            },
            'general': {
                'artificial_intelligence': "Artificial Intelligence (AI) refers to computer systems that can perform tasks typically requiring human intelligence, such as learning, reasoning, and problem-solving.",
                'machine_learning': "Machine Learning is a subset of AI where computers learn and improve from data without being explicitly programmed for every task.",
                'automation': "Automation uses technology to perform tasks with minimal human intervention, increasing efficiency and reducing errors in various processes."
            },
            'business': {
                'marketing': "Marketing involves promoting and selling products or services through various channels to reach and engage target customers.",
                'content_creation': "Content creation involves developing valuable, relevant material (text, images, videos) to attract and engage a specific audience.",
                'communication': "Effective business communication involves clear, professional messaging tailored to the audience and purpose."
            }
        }
    
    async def process_query(self, query: str, query_type: str = "auto") -> FallbackResponse:
        """Process a query using the fallback system"""
        start_time = time.time()
        
        if not self.initialized:
            await self.initialize()
        
        # Determine query type if auto
        if query_type == "auto":
            query_type = self._classify_query(query)
        
        # Generate response
        response = self._generate_response(query, query_type)
        processing_time = time.time() - start_time
        
        return FallbackResponse(
            response=response,
            confidence=0.7,
            processing_time=processing_time,
            source="iris_fallback",
            metadata={
                'query_type': query_type,
                'fallback_system': True,
                'knowledge_base_used': True
            }
        )
    
    def _classify_query(self, query: str) -> str:
        """Classify the query type"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['insurance', 'policy', 'claim', 'coverage', 'premium']):
            return 'insurance'
        elif any(word in query_lower for word in ['ai', 'artificial intelligence', 'machine learning', 'automation']):
            return 'general'
        elif any(word in query_lower for word in ['marketing', 'content', 'business', 'communication']):
            return 'business'
        else:
            return 'general'
    
    def _generate_response(self, query: str, query_type: str) -> str:
        """Generate a response based on the query and type"""
        query_lower = query.lower()
        
        # Check knowledge base for specific topics
        if query_type in self.knowledge_base:
            for topic, content in self.knowledge_base[query_type].items():
                if any(keyword in query_lower for keyword in topic.split('_')):
                    return content
        
        # Generate contextual responses
        if query_type == 'insurance':
            return self._generate_insurance_response(query)
        elif query_type == 'business':
            return self._generate_business_response(query)
        else:
            return self._generate_general_response(query)
    
    def _generate_insurance_response(self, query: str) -> str:
        """Generate insurance-specific responses"""
        query_lower = query.lower()
        
        if 'difference' in query_lower and 'between' in query_lower:
            return "Insurance products differ in coverage scope, cost, duration, and benefits. The best choice depends on your specific needs, budget, and risk tolerance. Consider factors like deductibles, coverage limits, and exclusions when comparing options."
        
        elif any(word in query_lower for word in ['cost', 'price', 'premium', 'expensive']):
            return "Insurance costs vary based on factors like age, health, location, coverage amount, and risk factors. To get accurate pricing, it's best to request quotes from multiple insurers and compare coverage options."
        
        elif any(word in query_lower for word in ['claim', 'file', 'process']):
            return "To file an insurance claim: 1) Contact your insurer immediately, 2) Document the incident with photos/reports, 3) Provide all requested information, 4) Keep records of all communications, 5) Follow up regularly on claim status."
        
        else:
            return "Insurance provides financial protection against various risks. Different types cover health, property, liability, and income protection. The right coverage depends on your individual circumstances and needs."
    
    def _generate_business_response(self, query: str) -> str:
        """Generate business-specific responses"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['write', 'create', 'content']):
            return "Content creation involves understanding your audience, researching topics thoroughly, crafting engaging headlines, providing valuable information, and including clear calls-to-action. Focus on solving problems or answering questions your audience has."
        
        elif any(word in query_lower for word in ['marketing', 'promote', 'advertise']):
            return "Effective marketing combines understanding your target audience, creating compelling messages, choosing the right channels, and measuring results. Focus on providing value and building relationships rather than just selling."
        
        elif any(word in query_lower for word in ['email', 'communication', 'message']):
            return "Professional communication should be clear, concise, and purposeful. Use appropriate tone, structure your message logically, include necessary details, and always proofread before sending."
        
        else:
            return "Business success often depends on understanding customer needs, providing excellent service, effective communication, and continuous improvement. Focus on building strong relationships and delivering value."
    
    def _generate_general_response(self, query: str) -> str:
        """Generate general responses"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['how', 'what', 'why', 'when', 'where']):
            return f"Regarding your question about '{query}': This is a complex topic that can be approached from multiple angles. For the most accurate and detailed information, I recommend consulting specialized resources or experts in the relevant field."
        
        elif any(word in query_lower for word in ['help', 'assist', 'support']):
            return "I'm here to help! I can provide information on insurance topics, business guidance, content creation, and general questions. Please feel free to ask specific questions, and I'll do my best to provide useful information."
        
        else:
            return f"Thank you for your question about '{query}'. While I can provide general guidance, for more detailed and specialized information, please specify the area you'd like help with (insurance, business, technology, etc.)."
    
    def get_capabilities(self) -> List[str]:
        """Get system capabilities"""
        return [
            "Basic insurance information and guidance",
            "General business and marketing advice",
            "Content creation assistance",
            "Professional communication help",
            "Technology and AI explanations",
            "General knowledge and information"
        ]
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        return {
            'initialized': self.initialized,
            'system_type': 'fallback',
            'capabilities': self.get_capabilities(),
            'knowledge_base_topics': list(self.knowledge_base.keys()),
            'advanced_features': False,
            'vision_capabilities': False,
            'web_automation': False
        }

class IRISFallbackDashboard:
    """Simple dashboard for the fallback system"""
    
    def __init__(self):
        self.fallback_system = IRISFallbackSystem()
        self.initialized = False
        
    async def initialize(self):
        """Initialize the fallback dashboard"""
        print("\n🚀 INITIALIZING IRIS FALLBACK SYSTEM")
        print("=" * 50)
        print("⚠️ Running in fallback mode with basic capabilities")
        
        await self.fallback_system.initialize()
        self.initialized = True
        
        print("✅ IRIS Fallback System ready!")
        return True
    
    async def process_query(self, query: str, query_type: str = "auto", **kwargs) -> Dict[str, Any]:
        """Process query using fallback system"""
        if not self.initialized:
            await self.initialize()
        
        try:
            result = await self.fallback_system.process_query(query, query_type)
            
            return {
                'success': True,
                'response': result.response,
                'confidence': result.confidence,
                'processing_time': result.processing_time,
                'processing_method': 'fallback_system',
                'source': result.source,
                'metadata': result.metadata
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response': f"I apologize, but I encountered an error processing your query: {query}",
                'processing_method': 'error_fallback'
            }
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return {
            'session_id': f"fallback_{int(time.time())}",
            'initialized': self.initialized,
            'system_status': {
                'advanced_models': False,
                'traditional_agents': False,
                'vision_capabilities': False,
                'knowledge_base': True,
                'mcp_servers': False,
                'web_automation': False
            },
            'capabilities': self.fallback_system.get_capabilities(),
            'config': {
                'fallback_mode': True,
                'basic_functionality': True
            }
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        self.initialized = False
        logger.info("IRIS Fallback System cleaned up")

# Global fallback instance
fallback_dashboard = None

async def get_fallback_dashboard():
    """Get or create fallback dashboard instance"""
    global fallback_dashboard
    
    if fallback_dashboard is None:
        fallback_dashboard = IRISFallbackDashboard()
        await fallback_dashboard.initialize()
    
    return fallback_dashboard

if __name__ == "__main__":
    async def test_fallback():
        """Test the fallback system"""
        dashboard = IRISFallbackDashboard()
        await dashboard.initialize()
        
        test_queries = [
            "What is life insurance?",
            "How does auto insurance work?",
            "What is artificial intelligence?",
            "Help me create marketing content",
            "How do I file an insurance claim?"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: {query}")
            result = await dashboard.process_query(query)
            print(f"✅ Response: {result['response'][:100]}...")
            print(f"⚡ Confidence: {result['confidence']:.2f}")
        
        await dashboard.cleanup()
    
    asyncio.run(test_fallback())
