#!/usr/bin/env python3
"""
IRIS Unified Dashboard
=====================

Main entry point for the IRIS system. Provides a comprehensive interface
for interacting with all IRIS capabilities including:
- Advanced AI models (MANUS, MiMo-VL-7B, Detail Flow, Giga Agent, Honest AI)
- Traditional RAG-based agents (Insurance, Content, Email, Social Media)
- Vision capabilities (web automation, document analysis, OCR)
- Knowledge management and MCP servers
- Intelligent query routing and response aggregation
"""

import asyncio
import logging
import time
import json
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import sys
import os

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('iris_dashboard.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import IRIS components
try:
    from enhanced_agent_interface import EnhancedAgentInterface
    from advanced_models.unified_interface import UnifiedModelInterface, ResponseStrategy, QueryType
    IRIS_AVAILABLE = True
    print("✅ IRIS system components loaded successfully")
except ImportError as e:
    IRIS_AVAILABLE = False
    print(f"⚠️ IRIS system not fully available: {e}")

class IRISDashboard:
    """Unified dashboard for the IRIS system"""
    
    def __init__(self):
        self.iris_interface = None
        self.session_id = f"iris_session_{int(time.time())}"
        self.query_history = []
        self.system_status = {}
        self.initialized = False
        
        # Dashboard configuration
        self.config = {
            'auto_route_queries': True,
            'enable_multi_agent': True,
            'enable_vision': True,
            'enable_web_automation': True,
            'response_aggregation': True,
            'save_history': True,
            'max_history': 100
        }
        
    async def initialize(self):
        """Initialize the IRIS dashboard and all components"""
        print("\n🚀 INITIALIZING IRIS UNIFIED DASHBOARD")
        print("=" * 60)
        
        if not IRIS_AVAILABLE:
            print("❌ IRIS system components not available")
            return False
        
        try:
            # Initialize enhanced agent interface
            self.iris_interface = EnhancedAgentInterface()
            await self.iris_interface.initialize()
            
            # Check system status
            await self._check_system_status()
            
            self.initialized = True
            
            # Display initialization results
            self._display_system_status()
            
            print("\n✅ IRIS Dashboard initialized successfully!")
            print(f"Session ID: {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Dashboard initialization failed: {e}")
            print(f"❌ Dashboard initialization failed: {e}")
            return False
    
    async def _check_system_status(self):
        """Check the status of all IRIS components"""
        self.system_status = {
            'advanced_models': False,
            'traditional_agents': False,
            'vision_capabilities': False,
            'knowledge_base': False,
            'mcp_servers': False,
            'web_automation': False
        }
        
        try:
            # Check advanced models
            if hasattr(self.iris_interface, 'unified_interface') and self.iris_interface.unified_interface:
                self.system_status['advanced_models'] = True
                
                # Check vision capabilities
                if hasattr(self.iris_interface.unified_interface, 'web_vision_agent'):
                    self.system_status['vision_capabilities'] = True
                    self.system_status['web_automation'] = True
            
            # Check traditional agents
            if hasattr(self.iris_interface, 'traditional_agents') and self.iris_interface.traditional_agents:
                self.system_status['traditional_agents'] = True
            
            # Check knowledge base
            if hasattr(self.iris_interface, 'knowledge_manager') and self.iris_interface.knowledge_manager:
                self.system_status['knowledge_base'] = True
            
            # Check MCP servers
            if hasattr(self.iris_interface, 'mcp_coordinator') and self.iris_interface.mcp_coordinator:
                self.system_status['mcp_servers'] = True
                
        except Exception as e:
            logger.warning(f"System status check failed: {e}")
    
    def _display_system_status(self):
        """Display the current system status"""
        print("\n📊 IRIS SYSTEM STATUS")
        print("-" * 40)
        
        status_items = [
            ("Advanced AI Models", self.system_status['advanced_models']),
            ("Traditional Agents", self.system_status['traditional_agents']),
            ("Vision Capabilities", self.system_status['vision_capabilities']),
            ("Knowledge Base", self.system_status['knowledge_base']),
            ("MCP Servers", self.system_status['mcp_servers']),
            ("Web Automation", self.system_status['web_automation'])
        ]
        
        for name, status in status_items:
            icon = "✅" if status else "❌"
            print(f"{icon} {name}")
        
        # Show available capabilities
        if self.system_status['advanced_models']:
            print("\n🤖 Advanced Models Available:")
            print("  • MANUS - Autonomous reasoning")
            print("  • MiMo-VL-7B - Vision-language processing")
            print("  • Detail Flow - Step-by-step analysis")
            print("  • Giga Agent - Autonomous agent capabilities")
            print("  • Honest AI - Research and fact-checking")
        
        if self.system_status['traditional_agents']:
            print("\n🏢 Traditional Agents Available:")
            print("  • Insurance Agent - Policy and claims")
            print("  • Content Agent - Marketing and content")
            print("  • Email Agent - Communication")
            print("  • Social Media Agent - Social platforms")
    
    async def process_query(
        self,
        query: str,
        query_type: str = "auto",
        image_data: Optional[bytes] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a query using the full IRIS system"""
        
        if not self.initialized:
            return {
                'success': False,
                'error': 'IRIS dashboard not initialized',
                'response': 'Please initialize the dashboard first'
            }
        
        start_time = time.time()
        query_id = f"query_{len(self.query_history) + 1}_{int(time.time())}"
        
        print(f"\n🔍 PROCESSING QUERY: {query_id}")
        print(f"Query: {query}")
        print(f"Type: {query_type}")
        if image_data:
            print(f"Image data: {len(image_data)} bytes")
        
        try:
            # Route query to appropriate processing method
            if query_type == "auto":
                query_type = self._analyze_query_type(query, image_data)
            
            print(f"Determined query type: {query_type}")
            
            # Process based on query type
            if query_type == "vision" and image_data:
                result = await self._process_vision_query(query, image_data, context)
            elif query_type == "insurance":
                result = await self._process_insurance_query(query, context)
            elif query_type == "research":
                result = await self._process_research_query(query, context)
            elif query_type == "web_automation":
                result = await self._process_web_automation_query(query, context)
            elif query_type == "complex":
                result = await self._process_complex_query(query, image_data, context)
            else:
                result = await self._process_general_query(query, image_data, context)
            
            processing_time = time.time() - start_time
            
            # Add metadata
            result.update({
                'query_id': query_id,
                'query_type': query_type,
                'processing_time': processing_time,
                'timestamp': datetime.now().isoformat()
            })
            
            # Save to history
            if self.config['save_history']:
                self._save_to_history(query, result)
            
            # Display result
            self._display_result(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Query processing failed: {e}")
            error_result = {
                'success': False,
                'error': str(e),
                'query_id': query_id,
                'processing_time': time.time() - start_time
            }
            
            self._display_result(error_result)
            return error_result
    
    def _analyze_query_type(self, query: str, image_data: Optional[bytes] = None) -> str:
        """Analyze query to determine the best processing approach"""
        query_lower = query.lower()
        
        # Vision queries
        if image_data or any(word in query_lower for word in ['image', 'photo', 'document', 'analyze', 'ocr', 'extract text']):
            return "vision"
        
        # Insurance queries
        if any(word in query_lower for word in ['insurance', 'policy', 'claim', 'premium', 'coverage', 'deductible']):
            return "insurance"
        
        # Research queries
        if any(word in query_lower for word in ['research', 'find information', 'fact check', 'verify', 'investigate']):
            return "research"
        
        # Web automation queries
        if any(word in query_lower for word in ['navigate', 'fill form', 'website', 'web page', 'click', 'submit']):
            return "web_automation"
        
        # Complex multi-step queries
        if any(word in query_lower for word in ['and then', 'after that', 'next step', 'workflow', 'process']):
            return "complex"
        
        return "general"
    
    async def _process_vision_query(self, query: str, image_data: bytes, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Process vision-related queries"""
        print("🔍 Processing with vision capabilities...")
        
        if not self.system_status['vision_capabilities']:
            return {
                'success': False,
                'error': 'Vision capabilities not available',
                'response': 'Vision processing is not currently available'
            }
        
        try:
            # Use unified interface for vision processing
            result = await self.iris_interface.unified_interface.query(
                query=query,
                image_data=image_data,
                strategy=ResponseStrategy.BEST_SINGLE
            )
            
            return {
                'success': True,
                'response': result.primary_response,
                'confidence': result.confidence,
                'models_used': [r.model_type.value for r in result.model_responses],
                'processing_method': 'vision_analysis'
            }
            
        except Exception as e:
            logger.error(f"Vision query processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': 'Vision processing encountered an error'
            }
    
    async def _process_insurance_query(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Process insurance-related queries"""
        print("🏢 Processing with insurance agents...")
        
        responses = []
        
        # Use traditional insurance agent if available
        if self.system_status['traditional_agents'] and 'insurance' in self.iris_interface.traditional_agents:
            try:
                traditional_result = await self.iris_interface.traditional_agents['insurance'].process_query(query)
                responses.append({
                    'source': 'traditional_insurance_agent',
                    'response': traditional_result.get('response', ''),
                    'confidence': traditional_result.get('confidence', 0.7)
                })
            except Exception as e:
                logger.warning(f"Traditional insurance agent failed: {e}")
        
        # Use advanced models if available
        if self.system_status['advanced_models']:
            try:
                advanced_result = await self.iris_interface.unified_interface.query(
                    query=f"Insurance query: {query}",
                    strategy=ResponseStrategy.CONSENSUS
                )
                responses.append({
                    'source': 'advanced_models',
                    'response': advanced_result.primary_response,
                    'confidence': advanced_result.confidence
                })
            except Exception as e:
                logger.warning(f"Advanced models failed: {e}")
        
        # Aggregate responses
        if responses:
            best_response = max(responses, key=lambda x: x['confidence'])
            return {
                'success': True,
                'response': best_response['response'],
                'confidence': best_response['confidence'],
                'sources_used': [r['source'] for r in responses],
                'processing_method': 'insurance_specialized'
            }
        else:
            return {
                'success': False,
                'error': 'No insurance processing capabilities available',
                'response': 'Insurance query processing is not currently available'
            }
    
    async def _process_research_query(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Process research and fact-checking queries"""
        print("🔬 Processing with research capabilities...")
        
        if self.system_status['advanced_models']:
            try:
                # Use Honest AI agent for research
                result = await self.iris_interface.unified_interface.query(
                    query=f"Research and fact-check: {query}",
                    strategy=ResponseStrategy.SPECIALIZED,
                    preferred_models=['honest_ai', 'giga_agent']
                )
                
                return {
                    'success': True,
                    'response': result.primary_response,
                    'confidence': result.confidence,
                    'models_used': [r.model_type.value for r in result.model_responses],
                    'processing_method': 'research_specialized'
                }
                
            except Exception as e:
                logger.error(f"Research query processing failed: {e}")
        
        return {
            'success': False,
            'error': 'Research capabilities not available',
            'response': 'Research processing is not currently available'
        }
    
    async def _process_web_automation_query(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Process web automation queries"""
        print("🌐 Processing with web automation...")
        
        if self.system_status['web_automation']:
            try:
                # Use web vision agent for automation
                result = await self.iris_interface.unified_interface.execute_visual_task(
                    task_description=query,
                    task_type="web_navigation"
                )
                
                return {
                    'success': result['success'],
                    'response': f"Web automation task completed. {result.get('result_data', {})}",
                    'execution_time': result.get('execution_time', 0),
                    'processing_method': 'web_automation'
                }
                
            except Exception as e:
                logger.error(f"Web automation failed: {e}")
        
        return {
            'success': False,
            'error': 'Web automation not available',
            'response': 'Web automation is not currently available'
        }
    
    async def _process_complex_query(self, query: str, image_data: Optional[bytes] = None, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Process complex multi-step queries"""
        print("🧠 Processing complex multi-agent query...")
        
        if self.system_status['advanced_models']:
            try:
                # Use multiple strategies for complex queries
                result = await self.iris_interface.unified_interface.query(
                    query=query,
                    image_data=image_data,
                    strategy=ResponseStrategy.PARALLEL,
                    query_type=QueryType.COMPLEX
                )
                
                return {
                    'success': True,
                    'response': result.primary_response,
                    'confidence': result.confidence,
                    'models_used': [r.model_type.value for r in result.model_responses],
                    'strategy_used': result.strategy_used.value,
                    'processing_method': 'complex_multi_agent'
                }
                
            except Exception as e:
                logger.error(f"Complex query processing failed: {e}")
        
        return await self._process_general_query(query, image_data, context)
    
    async def _process_general_query(self, query: str, image_data: Optional[bytes] = None, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Process general queries using best available method"""
        print("💭 Processing general query...")
        
        # Try advanced models first
        if self.system_status['advanced_models']:
            try:
                result = await self.iris_interface.unified_interface.query(
                    query=query,
                    image_data=image_data,
                    strategy=ResponseStrategy.BEST_SINGLE
                )
                
                return {
                    'success': True,
                    'response': result.primary_response,
                    'confidence': result.confidence,
                    'models_used': [r.model_type.value for r in result.model_responses],
                    'processing_method': 'advanced_models'
                }
                
            except Exception as e:
                logger.warning(f"Advanced models failed: {e}")
        
        # Fallback to traditional agents
        if self.system_status['traditional_agents']:
            try:
                # Use the most appropriate traditional agent
                agent_name = 'insurance'  # Default to insurance agent
                if agent_name in self.iris_interface.traditional_agents:
                    result = await self.iris_interface.traditional_agents[agent_name].process_query(query)
                    return {
                        'success': True,
                        'response': result.get('response', 'Query processed by traditional agent'),
                        'confidence': result.get('confidence', 0.6),
                        'processing_method': 'traditional_agent_fallback'
                    }
            except Exception as e:
                logger.warning(f"Traditional agent fallback failed: {e}")
        
        # Final fallback
        return {
            'success': False,
            'error': 'No processing capabilities available',
            'response': 'I apologize, but I cannot process this query at the moment. Please check system status.'
        }
    
    def _save_to_history(self, query: str, result: Dict[str, Any]):
        """Save query and result to history"""
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'query': query,
            'result': result
        }
        
        self.query_history.append(history_entry)
        
        # Limit history size
        if len(self.query_history) > self.config['max_history']:
            self.query_history = self.query_history[-self.config['max_history']:]
    
    def _display_result(self, result: Dict[str, Any]):
        """Display query result in a formatted way"""
        print("\n" + "=" * 60)
        print("📋 IRIS RESPONSE")
        print("=" * 60)
        
        if result.get('success', False):
            print(f"✅ Success: {result.get('response', 'No response')}")
            print(f"🎯 Confidence: {result.get('confidence', 0):.2f}")
            print(f"⚡ Processing Time: {result.get('processing_time', 0):.2f}s")
            print(f"🔧 Method: {result.get('processing_method', 'unknown')}")
            
            if 'models_used' in result:
                print(f"🤖 Models Used: {', '.join(result['models_used'])}")
            
            if 'sources_used' in result:
                print(f"📚 Sources: {', '.join(result['sources_used'])}")
                
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
            if 'response' in result:
                print(f"💬 Message: {result['response']}")
        
        print("=" * 60)
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information"""
        return {
            'session_id': self.session_id,
            'initialized': self.initialized,
            'system_status': self.system_status,
            'config': self.config,
            'query_history_count': len(self.query_history),
            'capabilities': self._get_available_capabilities()
        }
    
    def _get_available_capabilities(self) -> List[str]:
        """Get list of available capabilities"""
        capabilities = []
        
        if self.system_status['advanced_models']:
            capabilities.extend([
                "Advanced AI reasoning and analysis",
                "Multi-model response aggregation",
                "Autonomous task execution",
                "Research and fact-checking"
            ])
        
        if self.system_status['vision_capabilities']:
            capabilities.extend([
                "Image analysis and understanding",
                "Document processing and OCR",
                "Visual question answering"
            ])
        
        if self.system_status['web_automation']:
            capabilities.extend([
                "Web browser automation",
                "Form filling and navigation",
                "Visual web interaction"
            ])
        
        if self.system_status['traditional_agents']:
            capabilities.extend([
                "Insurance policy assistance",
                "Content creation and marketing",
                "Email communication",
                "Social media management"
            ])
        
        if self.system_status['knowledge_base']:
            capabilities.append("Domain knowledge retrieval")
        
        if self.system_status['mcp_servers']:
            capabilities.append("External tool integration")
        
        return capabilities
    
    async def cleanup(self):
        """Cleanup dashboard resources"""
        try:
            if self.iris_interface:
                await self.iris_interface.cleanup()
            
            # Save history if configured
            if self.config['save_history'] and self.query_history:
                history_file = f"iris_history_{self.session_id}.json"
                with open(history_file, 'w') as f:
                    json.dump(self.query_history, f, indent=2)
                print(f"💾 Query history saved to {history_file}")
            
            print("🧹 IRIS Dashboard cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

# Global dashboard instance
dashboard = None

async def main():
    """Main dashboard function"""
    global dashboard
    
    print("🚀 STARTING IRIS UNIFIED DASHBOARD")
    print("=" * 60)
    
    try:
        # Initialize dashboard
        dashboard = IRISDashboard()
        success = await dashboard.initialize()
        
        if not success:
            print("❌ Failed to initialize IRIS dashboard")
            return
        
        # Show usage instructions
        print("\n📖 IRIS DASHBOARD READY")
        print("=" * 60)
        print("You can now interact with IRIS using the following methods:")
        print("1. Use the interactive CLI (run with --interactive)")
        print("2. Import this module and use dashboard.process_query()")
        print("3. Use the web interface (if available)")
        print("\nExample usage:")
        print("  result = await dashboard.process_query('What is life insurance?')")
        print("  result = await dashboard.process_query('Analyze this image', image_data=image_bytes)")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n👋 IRIS Dashboard interrupted by user")
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        print(f"❌ Dashboard error: {e}")
    finally:
        if dashboard:
            await dashboard.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
