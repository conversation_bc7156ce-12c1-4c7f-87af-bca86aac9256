"""
Unified Model Interface
======================

Provides a unified interface for all advanced AI models with intelligent routing,
response aggregation, and performance optimization.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

from .model_manager import AdvancedModelManager, ModelType, ModelResponse

logger = logging.getLogger(__name__)

class ResponseStrategy(Enum):
    BEST_SINGLE = "best_single"
    CONSENSUS = "consensus"
    PARALLEL_ALL = "parallel_all"
    SEQUENTIAL = "sequential"
    SPECIALIZED = "specialized"

class QueryType(Enum):
    GENERAL = "general"
    VISION = "vision"
    RESEARCH = "research"
    AUTONOMOUS = "autonomous"
    FLOW_BASED = "flow_based"

@dataclass
class UnifiedResponse:
    primary_response: str
    confidence: float
    processing_time: float
    strategy_used: ResponseStrategy
    model_responses: List[ModelResponse]
    metadata: Dict[str, Any]

class UnifiedModelInterface:
    """Unified interface for all advanced AI models"""
    
    def __init__(self):
        self.model_manager = AdvancedModelManager()
        self.initialized = False
        self.config = {
            'default_strategy': ResponseStrategy.BEST_SINGLE,
            'consensus_threshold': 0.7,
            'parallel_timeout': 45.0,
            'enable_caching': True,
            'cache_ttl': 300  # 5 minutes
        }
        self.response_cache = {}
        self.performance_metrics = {}
        
    async def initialize(self):
        """Initialize the unified interface"""
        logger.info("Initializing Unified Model Interface...")
        
        try:
            await self.model_manager.initialize()
            self.initialized = True
            logger.info("Unified Model Interface initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Unified Model Interface: {e}")
            raise
    
    async def query(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        image_data: Optional[bytes] = None,
        strategy: Optional[ResponseStrategy] = None,
        query_type: Optional[QueryType] = None
    ) -> UnifiedResponse:
        """Main query interface with intelligent routing"""
        if not self.initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # Determine strategy and query type
            strategy = strategy or self._determine_strategy(query, image_data, query_type)
            query_type = query_type or self._classify_query(query, image_data)
            
            # Check cache if enabled
            if self.config['enable_caching']:
                cached_response = self._get_cached_response(query, context, image_data)
                if cached_response:
                    logger.info("Returning cached response")
                    return cached_response
            
            # Route query based on strategy
            if strategy == ResponseStrategy.BEST_SINGLE:
                response = await self._best_single_strategy(query, context, image_data, query_type)
            elif strategy == ResponseStrategy.CONSENSUS:
                response = await self._consensus_strategy(query, context, image_data)
            elif strategy == ResponseStrategy.PARALLEL_ALL:
                response = await self._parallel_all_strategy(query, context, image_data)
            elif strategy == ResponseStrategy.SEQUENTIAL:
                response = await self._sequential_strategy(query, context, image_data)
            elif strategy == ResponseStrategy.SPECIALIZED:
                response = await self._specialized_strategy(query, context, image_data, query_type)
            else:
                response = await self._best_single_strategy(query, context, image_data, query_type)
            
            # Cache response if enabled
            if self.config['enable_caching']:
                self._cache_response(query, context, image_data, response)
            
            # Update performance metrics
            self._update_metrics(strategy, time.time() - start_time, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Unified query error: {e}")
            
            # Return error response
            return UnifiedResponse(
                primary_response=f"Error processing query: {str(e)}",
                confidence=0.0,
                processing_time=time.time() - start_time,
                strategy_used=strategy or ResponseStrategy.BEST_SINGLE,
                model_responses=[],
                metadata={'error': True, 'error_message': str(e)}
            )
    
    def _determine_strategy(
        self, 
        query: str, 
        image_data: Optional[bytes], 
        query_type: Optional[QueryType]
    ) -> ResponseStrategy:
        """Determine optimal strategy based on query characteristics"""
        
        # Vision queries should use specialized strategy
        if image_data or query_type == QueryType.VISION:
            return ResponseStrategy.SPECIALIZED
        
        # Research queries benefit from consensus
        if query_type == QueryType.RESEARCH or any(word in query.lower() for word in ['research', 'analyze', 'compare', 'evaluate']):
            return ResponseStrategy.CONSENSUS
        
        # Autonomous queries should use specialized routing
        if query_type == QueryType.AUTONOMOUS or 'autonomous' in query.lower():
            return ResponseStrategy.SPECIALIZED
        
        # Complex queries benefit from parallel processing
        if len(query.split()) > 20 or '?' in query:
            return ResponseStrategy.PARALLEL_ALL
        
        # Default to best single for simple queries
        return ResponseStrategy.BEST_SINGLE
    
    def _classify_query(self, query: str, image_data: Optional[bytes]) -> QueryType:
        """Classify query type for optimal routing"""
        
        if image_data:
            return QueryType.VISION
        
        research_keywords = ['research', 'analyze', 'study', 'investigate', 'examine', 'compare']
        if any(keyword in query.lower() for keyword in research_keywords):
            return QueryType.RESEARCH
        
        autonomous_keywords = ['autonomous', 'independent', 'self-directed', 'automatic']
        if any(keyword in query.lower() for keyword in autonomous_keywords):
            return QueryType.AUTONOMOUS
        
        flow_keywords = ['step-by-step', 'process', 'workflow', 'detailed', 'flow']
        if any(keyword in query.lower() for keyword in flow_keywords):
            return QueryType.FLOW_BASED
        
        return QueryType.GENERAL
    
    async def _best_single_strategy(
        self, 
        query: str, 
        context: Optional[Dict], 
        image_data: Optional[bytes],
        query_type: QueryType
    ) -> UnifiedResponse:
        """Get best single response from optimal model"""
        
        best_response = await self.model_manager.get_best_response(query, context, image_data)
        
        return UnifiedResponse(
            primary_response=best_response.response,
            confidence=best_response.confidence,
            processing_time=best_response.processing_time,
            strategy_used=ResponseStrategy.BEST_SINGLE,
            model_responses=[best_response],
            metadata={
                'selected_model': best_response.model_type.value,
                'query_type': query_type.value
            }
        )
    
    async def _consensus_strategy(
        self, 
        query: str, 
        context: Optional[Dict], 
        image_data: Optional[bytes]
    ) -> UnifiedResponse:
        """Get consensus response from multiple models"""
        
        all_responses = await self.model_manager.query_all_models(query, context, image_data, parallel=True)
        
        # Filter valid responses
        valid_responses = [r for r in all_responses if r.error is None and r.confidence > 0.5]
        
        if not valid_responses:
            return await self._best_single_strategy(query, context, image_data, QueryType.GENERAL)
        
        # Calculate consensus
        consensus_response = self._calculate_consensus(valid_responses)
        total_time = sum(r.processing_time for r in all_responses)
        
        return UnifiedResponse(
            primary_response=consensus_response,
            confidence=self._calculate_consensus_confidence(valid_responses),
            processing_time=total_time,
            strategy_used=ResponseStrategy.CONSENSUS,
            model_responses=all_responses,
            metadata={
                'consensus_models': len(valid_responses),
                'total_models': len(all_responses)
            }
        )
    
    async def _parallel_all_strategy(
        self, 
        query: str, 
        context: Optional[Dict], 
        image_data: Optional[bytes]
    ) -> UnifiedResponse:
        """Get responses from all models in parallel"""
        
        all_responses = await self.model_manager.query_all_models(query, context, image_data, parallel=True)
        
        # Select best response
        valid_responses = [r for r in all_responses if r.error is None]
        if valid_responses:
            best_response = max(valid_responses, key=lambda r: r.confidence)
            primary_response = best_response.response
            confidence = best_response.confidence
        else:
            primary_response = "No valid responses received from models"
            confidence = 0.0
        
        total_time = max(r.processing_time for r in all_responses) if all_responses else 0.0
        
        return UnifiedResponse(
            primary_response=primary_response,
            confidence=confidence,
            processing_time=total_time,
            strategy_used=ResponseStrategy.PARALLEL_ALL,
            model_responses=all_responses,
            metadata={
                'models_queried': len(all_responses),
                'valid_responses': len(valid_responses)
            }
        )
    
    async def _sequential_strategy(
        self, 
        query: str, 
        context: Optional[Dict], 
        image_data: Optional[bytes]
    ) -> UnifiedResponse:
        """Get responses sequentially until satisfactory result"""
        
        model_order = [ModelType.HONEST_AI, ModelType.GIGA_AGENT, ModelType.MANUS, ModelType.DETAIL_FLOW, ModelType.MIMO_VL]
        responses = []
        total_time = 0
        
        for model_type in model_order:
            response = await self.model_manager.query_single_model(model_type, query, context, image_data)
            responses.append(response)
            total_time += response.processing_time
            
            # Stop if we get a high-confidence response
            if response.error is None and response.confidence > 0.8:
                break
        
        # Select best response
        valid_responses = [r for r in responses if r.error is None]
        if valid_responses:
            best_response = max(valid_responses, key=lambda r: r.confidence)
            primary_response = best_response.response
            confidence = best_response.confidence
        else:
            primary_response = "No valid responses received"
            confidence = 0.0
        
        return UnifiedResponse(
            primary_response=primary_response,
            confidence=confidence,
            processing_time=total_time,
            strategy_used=ResponseStrategy.SEQUENTIAL,
            model_responses=responses,
            metadata={
                'models_tried': len(responses),
                'early_termination': len(responses) < len(model_order)
            }
        )
    
    async def _specialized_strategy(
        self, 
        query: str, 
        context: Optional[Dict], 
        image_data: Optional[bytes],
        query_type: QueryType
    ) -> UnifiedResponse:
        """Route to specialized model based on query type"""
        
        # Route to appropriate specialized model
        if query_type == QueryType.VISION and image_data:
            model_type = ModelType.MIMO_VL
        elif query_type == QueryType.RESEARCH:
            model_type = ModelType.HONEST_AI
        elif query_type == QueryType.AUTONOMOUS:
            model_type = ModelType.GIGA_AGENT
        elif query_type == QueryType.FLOW_BASED:
            model_type = ModelType.DETAIL_FLOW
        else:
            model_type = ModelType.MANUS
        
        response = await self.model_manager.query_single_model(model_type, query, context, image_data)
        
        return UnifiedResponse(
            primary_response=response.response,
            confidence=response.confidence,
            processing_time=response.processing_time,
            strategy_used=ResponseStrategy.SPECIALIZED,
            model_responses=[response],
            metadata={
                'specialized_model': model_type.value,
                'query_type': query_type.value
            }
        )
    
    def _calculate_consensus(self, responses: List[ModelResponse]) -> str:
        """Calculate consensus response from multiple models"""
        
        # For now, return the highest confidence response
        # In a more sophisticated implementation, this could analyze
        # response similarity and create a merged response
        
        best_response = max(responses, key=lambda r: r.confidence)
        
        consensus_intro = f"Consensus from {len(responses)} models:\\n\\n"
        return consensus_intro + best_response.response
    
    def _calculate_consensus_confidence(self, responses: List[ModelResponse]) -> float:
        """Calculate consensus confidence score"""
        
        if not responses:
            return 0.0
        
        # Average confidence weighted by individual confidence scores
        total_weight = sum(r.confidence for r in responses)
        if total_weight == 0:
            return 0.0
        
        weighted_confidence = sum(r.confidence * r.confidence for r in responses) / total_weight
        return min(weighted_confidence, 1.0)
    
    def _get_cached_response(
        self, 
        query: str, 
        context: Optional[Dict], 
        image_data: Optional[bytes]
    ) -> Optional[UnifiedResponse]:
        """Get cached response if available"""
        
        # Create cache key
        cache_key = self._create_cache_key(query, context, image_data)
        
        if cache_key in self.response_cache:
            cached_item = self.response_cache[cache_key]
            
            # Check if cache is still valid
            if time.time() - cached_item['timestamp'] < self.config['cache_ttl']:
                return cached_item['response']
            else:
                # Remove expired cache entry
                del self.response_cache[cache_key]
        
        return None
    
    def _cache_response(
        self, 
        query: str, 
        context: Optional[Dict], 
        image_data: Optional[bytes],
        response: UnifiedResponse
    ):
        """Cache response for future use"""
        
        cache_key = self._create_cache_key(query, context, image_data)
        
        self.response_cache[cache_key] = {
            'response': response,
            'timestamp': time.time()
        }
        
        # Limit cache size
        if len(self.response_cache) > 100:
            # Remove oldest entries
            oldest_keys = sorted(
                self.response_cache.keys(),
                key=lambda k: self.response_cache[k]['timestamp']
            )[:20]
            
            for key in oldest_keys:
                del self.response_cache[key]
    
    def _create_cache_key(
        self, 
        query: str, 
        context: Optional[Dict], 
        image_data: Optional[bytes]
    ) -> str:
        """Create cache key for query"""
        
        import hashlib
        
        key_parts = [query]
        
        if context:
            key_parts.append(str(sorted(context.items())))
        
        if image_data:
            # Use hash of image data
            key_parts.append(hashlib.md5(image_data).hexdigest())
        
        return hashlib.md5('|'.join(key_parts).encode()).hexdigest()
    
    def _update_metrics(self, strategy: ResponseStrategy, processing_time: float, response: UnifiedResponse):
        """Update performance metrics"""
        
        strategy_key = strategy.value
        
        if strategy_key not in self.performance_metrics:
            self.performance_metrics[strategy_key] = {
                'total_queries': 0,
                'total_time': 0.0,
                'average_confidence': 0.0,
                'success_rate': 0.0
            }
        
        metrics = self.performance_metrics[strategy_key]
        metrics['total_queries'] += 1
        metrics['total_time'] += processing_time
        
        # Update running averages
        total_queries = metrics['total_queries']
        metrics['average_confidence'] = (
            (metrics['average_confidence'] * (total_queries - 1) + response.confidence) / total_queries
        )
        
        success = 1.0 if response.confidence > 0.5 else 0.0
        metrics['success_rate'] = (
            (metrics['success_rate'] * (total_queries - 1) + success) / total_queries
        )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for all strategies"""
        return self.performance_metrics.copy()
    
    def get_model_status(self) -> Dict[str, Any]:
        """Get status of all models"""
        return self.model_manager.get_model_status()
    
    async def cleanup(self):
        """Cleanup unified interface"""
        try:
            await self.model_manager.cleanup()
            self.response_cache.clear()
            self.performance_metrics.clear()
            self.initialized = False
            
            logger.info("Unified Model Interface cleaned up")
            
        except Exception as e:
            logger.error(f"Error cleaning up Unified Model Interface: {e}")
