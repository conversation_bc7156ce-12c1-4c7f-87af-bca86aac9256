#!/usr/bin/env python3
"""
Enhanced Agent Interface with Advanced Models
=============================================

Provides a unified interface that integrates all advanced AI models for optimal responses.
This interface automatically routes queries to the best models and aggregates responses.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
import json

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import advanced models
try:
    from advanced_models.unified_interface import UnifiedModelInterface, ResponseStrategy, QueryType
    from advanced_models.model_manager import AdvancedModelManager, ModelType
    ADVANCED_MODELS_AVAILABLE = True
except ImportError:
    ADVANCED_MODELS_AVAILABLE = False
    logger.warning("Advanced models not available. Run: python install_advanced_models.py")

# Import existing agent system components
try:
    from rag_agent_system import (
        InsuranceMainAgent, ContentMainAgent, EmailMainAgent, SocialMediaMainAgent
    )
    from knowledge_management import KnowledgeManager, DomainKnowledgeBase
    from mcp_coordinator import coordinator as mcp_coordinator
    TRADITIONAL_AGENTS_AVAILABLE = True
except ImportError as e:
    TRADITIONAL_AGENTS_AVAILABLE = False
    logger.warning(f"Traditional agents not available: {e}")

@dataclass
class EnhancedResponse:
    """Enhanced response with multiple model outputs"""
    primary_response: str
    confidence: float
    processing_time: float
    models_used: List[str]
    strategy_used: str
    alternative_responses: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class EnhancedAgentInterface:
    """Enhanced interface that combines traditional agents with advanced AI models"""

    def __init__(self):
        # Initialize component availability
        self.traditional_agents = {}
        self.knowledge_manager = None
        self.mcp_coordinator = None
        self.unified_interface = None
        self.initialized = False

        self.config = {
            'use_advanced_models': ADVANCED_MODELS_AVAILABLE,
            'use_traditional_agents': TRADITIONAL_AGENTS_AVAILABLE,
            'enable_knowledge_base': True,
            'enable_mcp_servers': True,
            'response_aggregation': True,
            'performance_tracking': True
        }
        self.performance_metrics = {}
        
    async def initialize(self):
        """Initialize the enhanced interface with all components"""
        logger.info("Initializing IRIS Enhanced Agent Interface...")

        try:
            # Initialize advanced models if available
            if ADVANCED_MODELS_AVAILABLE and self.config['use_advanced_models']:
                self.unified_interface = UnifiedModelInterface()
                await self.unified_interface.initialize()
                logger.info("✅ Advanced AI models initialized")

            # Initialize traditional agents if available
            if TRADITIONAL_AGENTS_AVAILABLE and self.config['use_traditional_agents']:
                await self._initialize_traditional_agents()
                logger.info("✅ Traditional agents initialized")

            # Initialize knowledge management
            if self.config['enable_knowledge_base']:
                await self._initialize_knowledge_base()
                logger.info("✅ Knowledge base initialized")

            # Initialize MCP servers
            if self.config['enable_mcp_servers']:
                await self._initialize_mcp_servers()
                logger.info("✅ MCP servers initialized")

            self.initialized = True
            logger.info("🚀 IRIS Enhanced Agent Interface initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize IRIS Enhanced Agent Interface: {e}")
            # Partial initialization is acceptable
            self.initialized = True
            logger.info("⚠️ IRIS initialized with limited capabilities")

    async def _initialize_traditional_agents(self):
        """Initialize traditional RAG-based agents"""
        try:
            if TRADITIONAL_AGENTS_AVAILABLE:
                # Initialize insurance agent
                self.traditional_agents['insurance'] = InsuranceMainAgent()

                # Initialize content agent
                self.traditional_agents['content'] = ContentMainAgent()

                # Initialize email agent
                self.traditional_agents['email'] = EmailMainAgent()

                # Initialize social media agent
                self.traditional_agents['social_media'] = SocialMediaMainAgent()

                logger.info(f"Initialized {len(self.traditional_agents)} traditional agents")
        except Exception as e:
            logger.warning(f"Traditional agents initialization failed: {e}")

    async def _initialize_knowledge_base(self):
        """Initialize knowledge management system"""
        try:
            if TRADITIONAL_AGENTS_AVAILABLE:
                self.knowledge_manager = KnowledgeManager()
                # Initialize domain knowledge bases
                await self.knowledge_manager.initialize()
                logger.info("Knowledge management system initialized")
        except Exception as e:
            logger.warning(f"Knowledge base initialization failed: {e}")

    async def _initialize_mcp_servers(self):
        """Initialize MCP server coordination"""
        try:
            if TRADITIONAL_AGENTS_AVAILABLE:
                self.mcp_coordinator = mcp_coordinator
                # Initialize MCP server connections
                await self.mcp_coordinator.initialize()
                logger.info("MCP servers initialized")
        except Exception as e:
            logger.warning(f"MCP servers initialization failed: {e}")
    
    async def process_query(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        image_data: Optional[bytes] = None,
        agent_type: Optional[str] = None,
        strategy: Optional[str] = None
    ) -> EnhancedResponse:
        """Process query using the best available models and agents"""
        if not self.initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # Determine query type and strategy
            query_type = self._classify_query(query, image_data, agent_type)
            response_strategy = self._determine_strategy(query, strategy, query_type)
            
            # Get responses from different sources
            responses = []
            
            # Get advanced models response if available
            if self.unified_interface and self.config['use_advanced_models']:
                advanced_response = await self._get_advanced_response(
                    query, context, image_data, response_strategy, query_type
                )
                if advanced_response:
                    responses.append(advanced_response)
            
            # Get traditional agent response if needed
            if self.config.get('fallback_to_traditional', True) or not responses:
                traditional_response = await self._get_traditional_response(
                    query, context, agent_type
                )
                if traditional_response:
                    responses.append(traditional_response)
            
            # Aggregate responses
            final_response = self._aggregate_responses(responses, response_strategy)
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self._update_performance_metrics(query_type, processing_time, final_response)
            
            return final_response
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            processing_time = time.time() - start_time
            
            return EnhancedResponse(
                primary_response=f"Error processing query: {str(e)}",
                confidence=0.0,
                processing_time=processing_time,
                models_used=[],
                strategy_used="error",
                alternative_responses=[],
                metadata={'error': True, 'error_message': str(e)}
            )
    
    def _classify_query(self, query: str, image_data: Optional[bytes], agent_type: Optional[str]) -> str:
        """Classify query type for optimal routing"""
        
        if image_data:
            return "vision"
        
        if agent_type:
            return agent_type
        
        # Analyze query content
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['insurance', 'policy', 'claim', 'coverage']):
            return "insurance"
        elif any(word in query_lower for word in ['research', 'analyze', 'study', 'investigate']):
            return "research"
        elif any(word in query_lower for word in ['autonomous', 'independent', 'automatic']):
            return "autonomous"
        elif any(word in query_lower for word in ['step-by-step', 'process', 'workflow', 'detailed']):
            return "flow_based"
        elif any(word in query_lower for word in ['content', 'write', 'create', 'marketing']):
            return "content"
        elif any(word in query_lower for word in ['email', 'message', 'communication']):
            return "communication"
        else:
            return "general"
    
    def _determine_strategy(self, query: str, strategy: Optional[str], query_type: str) -> str:
        """Determine optimal response strategy"""
        
        if strategy:
            return strategy
        
        # Auto-determine strategy based on query characteristics
        if query_type == "vision":
            return "specialized"
        elif query_type in ["research", "autonomous"]:
            return "consensus"
        elif len(query.split()) > 20:
            return "parallel_all"
        else:
            return "best_single"
    
    async def _get_advanced_response(
        self,
        query: str,
        context: Optional[Dict],
        image_data: Optional[bytes],
        strategy: str,
        query_type: str
    ) -> Optional[Dict[str, Any]]:
        """Get response from advanced models"""
        try:
            # Map strategy string to enum
            strategy_map = {
                "best_single": ResponseStrategy.BEST_SINGLE,
                "consensus": ResponseStrategy.CONSENSUS,
                "parallel_all": ResponseStrategy.PARALLEL_ALL,
                "sequential": ResponseStrategy.SEQUENTIAL,
                "specialized": ResponseStrategy.SPECIALIZED
            }
            
            # Map query type string to enum
            query_type_map = {
                "vision": QueryType.VISION,
                "research": QueryType.RESEARCH,
                "autonomous": QueryType.AUTONOMOUS,
                "flow_based": QueryType.FLOW_BASED,
                "general": QueryType.GENERAL
            }
            
            result = await self.unified_interface.query(
                query=query,
                context=context,
                image_data=image_data,
                strategy=strategy_map.get(strategy, ResponseStrategy.BEST_SINGLE),
                query_type=query_type_map.get(query_type, QueryType.GENERAL)
            )
            
            return {
                'source': 'advanced_models',
                'response': result.primary_response,
                'confidence': result.confidence,
                'processing_time': result.processing_time,
                'models_used': [r.model_type.value for r in result.model_responses],
                'strategy_used': result.strategy_used.value,
                'metadata': result.metadata
            }
            
        except Exception as e:
            logger.error(f"Error getting advanced response: {e}")
            return None
    
    async def _get_traditional_response(
        self,
        query: str,
        context: Optional[Dict],
        agent_type: Optional[str]
    ) -> Optional[Dict[str, Any]]:
        """Get response from traditional agent system"""
        try:
            response = None

            # Route to appropriate traditional agent
            if agent_type == "insurance" and 'insurance' in self.traditional_agents:
                agent = self.traditional_agents['insurance']
                if hasattr(agent, 'process_query'):
                    try:
                        result = await agent.process_query(query, context)
                        response = result if isinstance(result, str) else str(result)
                    except:
                        response = f"Insurance analysis: {query}"
                else:
                    response = f"Insurance expertise applied to: {query}"

            elif agent_type == "content" and 'content' in self.traditional_agents:
                agent = self.traditional_agents['content']
                if hasattr(agent, 'create_content'):
                    try:
                        result = await agent.create_content(query, context)
                        response = result if isinstance(result, str) else str(result)
                    except:
                        response = f"Content created for: {query}"
                else:
                    response = f"Content creation: {query}"

            elif agent_type == "communication" and 'email' in self.traditional_agents:
                agent = self.traditional_agents['email']
                if hasattr(agent, 'process_message'):
                    try:
                        result = await agent.process_message(query, context)
                        response = result if isinstance(result, str) else str(result)
                    except:
                        response = f"Communication handled: {query}"
                else:
                    response = f"Email communication: {query}"

            # Fallback response using basic knowledge
            if not response:
                response = self._generate_fallback_response(query, agent_type)

            return {
                'source': 'traditional_agents',
                'response': response,
                'confidence': 0.7,
                'processing_time': 0.1,
                'agent_used': agent_type or 'general',
                'metadata': {'traditional_agent': True}
            }

        except Exception as e:
            logger.error(f"Error getting traditional response: {e}")
            # Return a basic fallback response
            return {
                'source': 'fallback',
                'response': self._generate_fallback_response(query, agent_type),
                'confidence': 0.5,
                'processing_time': 0.05,
                'agent_used': 'fallback',
                'metadata': {'fallback': True, 'error': str(e)}
            }

    def _generate_fallback_response(self, query: str, agent_type: Optional[str]) -> str:
        """Generate a basic fallback response"""
        query_lower = query.lower()

        if agent_type == "insurance" or any(word in query_lower for word in ['insurance', 'policy', 'claim', 'coverage']):
            if 'life insurance' in query_lower:
                return "Life insurance provides financial protection for your beneficiaries in case of your death. There are two main types: term life insurance (temporary coverage) and whole life insurance (permanent coverage with cash value)."
            elif 'auto insurance' in query_lower:
                return "Auto insurance protects you financially in case of car accidents, theft, or damage. Most states require minimum liability coverage, but comprehensive and collision coverage provide additional protection."
            elif 'health insurance' in query_lower:
                return "Health insurance helps cover medical expenses including doctor visits, hospital stays, prescriptions, and preventive care. Plans vary in coverage levels and cost-sharing arrangements."
            else:
                return "Insurance provides financial protection against various risks. Different types of insurance cover different aspects of life including health, property, liability, and income protection."

        elif agent_type == "content" or any(word in query_lower for word in ['write', 'create', 'content', 'marketing']):
            return f"Content creation request: {query}. This would typically involve researching the topic, understanding the target audience, and crafting engaging, informative content that meets the specified requirements."

        elif agent_type == "communication" or any(word in query_lower for word in ['email', 'message', 'communication']):
            return f"Communication assistance: {query}. This involves crafting clear, professional, and effective messages tailored to the intended audience and purpose."

        else:
            return f"I understand you're asking about: {query}. While I can provide general assistance, for more detailed and specialized information, please specify the type of help you need (insurance, content creation, communication, etc.)."
    
    def _aggregate_responses(self, responses: List[Dict[str, Any]], strategy: str) -> EnhancedResponse:
        """Aggregate multiple responses into final response"""
        
        if not responses:
            return EnhancedResponse(
                primary_response="No responses available",
                confidence=0.0,
                processing_time=0.0,
                models_used=[],
                strategy_used=strategy,
                alternative_responses=[],
                metadata={'error': True}
            )
        
        # Sort responses by confidence
        responses.sort(key=lambda r: r.get('confidence', 0), reverse=True)
        
        # Select primary response (highest confidence)
        primary = responses[0]
        
        # Collect alternative responses
        alternatives = responses[1:] if len(responses) > 1 else []
        
        # Aggregate models used
        models_used = []
        total_processing_time = 0
        
        for response in responses:
            if 'models_used' in response:
                models_used.extend(response['models_used'])
            elif 'agent_used' in response:
                models_used.append(response['agent_used'])
            
            total_processing_time += response.get('processing_time', 0)
        
        # Calculate aggregated confidence
        if len(responses) > 1 and self.config['response_aggregation']:
            # Weighted average confidence
            total_weight = sum(r.get('confidence', 0) for r in responses)
            if total_weight > 0:
                aggregated_confidence = sum(
                    r.get('confidence', 0) * r.get('confidence', 0) for r in responses
                ) / total_weight
            else:
                aggregated_confidence = primary.get('confidence', 0)
        else:
            aggregated_confidence = primary.get('confidence', 0)
        
        return EnhancedResponse(
            primary_response=primary['response'],
            confidence=min(aggregated_confidence, 1.0),
            processing_time=total_processing_time,
            models_used=list(set(models_used)),  # Remove duplicates
            strategy_used=strategy,
            alternative_responses=alternatives,
            metadata={
                'response_count': len(responses),
                'primary_source': primary['source'],
                'aggregation_enabled': self.config['response_aggregation']
            }
        )
    
    def _update_performance_metrics(self, query_type: str, processing_time: float, response: EnhancedResponse):
        """Update performance metrics"""
        if not self.config['performance_tracking']:
            return
        
        if query_type not in self.performance_metrics:
            self.performance_metrics[query_type] = {
                'total_queries': 0,
                'total_time': 0.0,
                'average_confidence': 0.0,
                'success_rate': 0.0
            }
        
        metrics = self.performance_metrics[query_type]
        metrics['total_queries'] += 1
        metrics['total_time'] += processing_time
        
        # Update running averages
        total_queries = metrics['total_queries']
        metrics['average_confidence'] = (
            (metrics['average_confidence'] * (total_queries - 1) + response.confidence) / total_queries
        )
        
        success = 1.0 if response.confidence > 0.5 else 0.0
        metrics['success_rate'] = (
            (metrics['success_rate'] * (total_queries - 1) + success) / total_queries
        )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return self.performance_metrics.copy()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        status = {
            'initialized': self.initialized,
            'advanced_models_available': ADVANCED_MODELS_AVAILABLE,
            'config': self.config.copy()
        }
        
        if self.unified_interface:
            status['advanced_models_status'] = self.unified_interface.get_model_status()
            status['advanced_models_performance'] = self.unified_interface.get_performance_metrics()
        
        if self.traditional_agents:
            status['traditional_agents'] = list(self.traditional_agents.keys())

        status['traditional_agents_available'] = TRADITIONAL_AGENTS_AVAILABLE
        status['performance_metrics'] = self.performance_metrics
        
        return status
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.unified_interface:
                await self.unified_interface.cleanup()
            
            self.performance_metrics.clear()
            self.initialized = False
            
            logger.info("Enhanced Agent Interface cleaned up")
            
        except Exception as e:
            logger.error(f"Error cleaning up Enhanced Agent Interface: {e}")

# Convenience functions for easy usage
async def query_enhanced_agents(
    query: str,
    context: Optional[Dict[str, Any]] = None,
    image_data: Optional[bytes] = None,
    agent_type: Optional[str] = None,
    strategy: Optional[str] = None
) -> EnhancedResponse:
    """Convenience function to query enhanced agents"""
    interface = EnhancedAgentInterface()
    await interface.initialize()
    
    try:
        return await interface.process_query(
            query=query,
            context=context,
            image_data=image_data,
            agent_type=agent_type,
            strategy=strategy
        )
    finally:
        await interface.cleanup()

def get_available_models() -> List[str]:
    """Get list of available models"""
    models = []
    
    if ADVANCED_MODELS_AVAILABLE:
        models.extend([
            "MANUS (OpenManus)",
            "MiMo-VL-7B (Xiaomi Vision-Language)",
            "Detail Flow (ByteDance)",
            "Giga Agent (Abacus.ai)",
            "Honest AI Agent (Google)"
        ])
    
    models.extend([
        "Traditional Insurance Agent",
        "Traditional Content Agent",
        "Traditional Communication Agent",
        "Traditional Social Media Agent"
    ])
    
    return models

# Example usage
async def main():
    """Example usage of the enhanced interface"""
    interface = EnhancedAgentInterface()
    await interface.initialize()
    
    try:
        # Test queries
        test_queries = [
            {
                'query': "What are the benefits of life insurance?",
                'agent_type': "insurance"
            },
            {
                'query': "Analyze the current market trends in renewable energy",
                'agent_type': "research"
            },
            {
                'query': "Create a step-by-step guide for filing an insurance claim",
                'agent_type': "flow_based"
            }
        ]
        
        for test in test_queries:
            print(f"\\nQuery: {test['query']}")
            print(f"Agent Type: {test['agent_type']}")
            print("-" * 50)
            
            response = await interface.process_query(
                query=test['query'],
                agent_type=test['agent_type']
            )
            
            print(f"Response: {response.primary_response[:200]}...")
            print(f"Confidence: {response.confidence:.2f}")
            print(f"Models Used: {response.models_used}")
            print(f"Processing Time: {response.processing_time:.2f}s")
        
        # Print system status
        print("\\n" + "="*60)
        print("SYSTEM STATUS")
        print("="*60)
        status = interface.get_system_status()
        print(json.dumps(status, indent=2, default=str))
        
    finally:
        await interface.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
